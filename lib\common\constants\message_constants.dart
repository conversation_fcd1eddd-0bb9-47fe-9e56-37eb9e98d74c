/// 消息常量类
/// 
/// 统一管理应用中的错误信息和提示文本
class MessageConstants {
  // 错误消息
  static const String networkError = 'Network connection error, please check your network and try again';
  static const String requestFailed = 'Request failed, please try again later';
  static const String loadFailed = 'Load failed, please try again';
  static const String loadMoreFailed = 'Load more failed, please try again';
  static const String saveFailed = 'Save failed, please try again';
  static const String deleteFailed = 'Delete failed, please try again';
  static const String operationFailed = 'Operation failed, please try again';
  static const String unknownError = 'Unknown error, please try again';
  static const String requestTimeout = 'Request timed out, please try again later';
  
  // 聊天相关消息
  static const String connectingMessage = 'Establishing real-time connection...';
  static const String connectionSuccessMessage = 'Connection successful, you can start chatting';
  static const String connectionFailedMessage = 'Connection failed, please try again later';
  static const String connectionLostMessage = 'The connection to the server has been lost';
  static const String reconnectingMessage = 'Reconnecting...';
  static const String lastReconnectAttemptMessage = 'Connection unstable, trying last reconnect...';
  static const String loadingHistoryMessage = 'Loading chat history...';
  static const String loadHistoryFailedMessage = 'Failed to load chat history, trying to establish real-time connection';
  static const String noMoreRolesMessage = 'No more roles available';
  static const String switchRoleFailedMessage = 'Switch role failed, please try again';
  static const String creatingNewChatMessage = 'Creating new chat session...';
  static const String chatStartFailedMessage = 'Failed to create chat session, please try again';
  
  // 会话相关消息
  static const String noSessionsMessage = 'No chat history';
  static const String deleteSessionSuccessMessage = 'Session deleted';
  static const String deleteSessionFailedMessage = 'Failed to delete session';
  static const String sessionDeleteConfirmMessage = 'Are you sure you want to delete this session?';
  static const String createSessionFailedMessage = 'Failed to create session, please try again';
  static const String createSessionSuccessMessage = 'Session created successfully';
  
  // 推荐相关消息
  static const String noRecommendedRolesMessage = 'No AI Roles available\nYou can try again later';
  static const String loadRecommendedRolesFailedMessage = 'Failed to load recommended roles, please try again';
  static const String allRolesLoadedMessage = 'All recommended roles have been loaded';
  static const String noMoreDataMessage = 'No more roles';
  static const String enterChatFailedMessage = 'Failed to enter chat, please try again';
  static const String roleLoadingMessage = 'Loading AI roles...';
  static const String roleImageLoadFailedMessage = 'Failed to load role image';
  static const String retryLoadingMessage = 'Click to reload';
  static const String refreshingRolesMessage = 'Refreshing role list...';
  static const String roleDataErrorMessage = 'Role data parsing error';
  static const String cacheExpiredMessage = 'Cache expired, reloading...';

  // 认证相关消息
  static const String authInProgressMessage = 'Authentication in progress, please wait';

  // 收藏相关消息
  static const String favoriteFailedMessage = 'Failed to add to collection, please try again';
  static const String unfavoriteFailedMessage = 'Failed to remove from collection, please try again';
  static const String favoriteSuccessMessage = 'Added to collection';
  static const String unfavoriteSuccessMessage = 'Removed from collection';

  // 登录相关消息
  static const String emailRequiredMessage = 'Please enter email address';
  static const String passwordRequiredMessage = 'Please enter password';

  // 报告相关消息
  static const String reportSubmissionFailedMessage = 'Report submission failed. Please try again.';
  static const String reportFunctionUnavailableMessage = 'Report function unavailable, please try again later';

  // 图片上传相关消息
  static const String imageSelectFailedMessage = 'Failed to select image. Please try again.';

  // 超时相关消息
  static const String connectionTimeoutMessage = 'Connection timed out. Please check your network and try again.';
  static const String receiveTimeoutMessage = 'Server response timed out. The server might be busy, please try again later.';
  static const String sendTimeoutMessage = 'Request sending timed out. Please check your network and try again.';
  static const String asyncTimeoutMessage = 'Operation timed out. Please try again.';
  static const String generalTimeoutMessage = 'Request timed out. Please try again later.';
  static const String asyncOperationTimeoutMessage = 'Operation timed out. Please try again.';

  // WebSocket相关消息
  static const String webSocketConnectionErrorMessage = 'WebSocket connection error';
  static const String webSocketConnectionFailedMessage = 'WebSocket connection failed';

  // 网络相关消息
  static const String networkConnectionErrorMessage = 'Network connection error';

  // 置顶操作消息
  static const String pinOperationInProgressMessage = 'Pin operation in progress';
  static const String pinOperationFailedMessage = 'Failed to update pin status';
  static const String pinOperationSuccessMessage = 'Pin status updated successfully';

  // 通用操作消息
  static const String unexpectedErrorMessage = 'An unexpected error occurred';
  static const String errorProcessingMessage = '处理异常时发生错误';

  // 成功消息
  static const String operationSuccessMessage = 'Operation successful';
  
  // 提示消息
  static const String confirmMessage = 'Confirm';
  static const String cancelMessage = 'Cancel';
  static const String tipMessage = 'Tip';
  static const String warningMessage = 'Warning';
  static const String errorMessage = 'Error';
  static const String successMessage = 'Success';
}